{"description": "项目私有配置文件。此文件中的内容将覆盖 project.config.json 中的相同字段。项目的改动优先同步到此文件中。详见文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/projectconfig.html", "projectname": "%E4%B8%80%E7%81%AF%E5%A4%A7%E5%B8%88", "setting": {"compileHotReLoad": false, "urlCheck": false, "coverView": true, "lazyloadPlaceholderEnable": false, "skylineRenderEnable": false, "preloadBackgroundData": false, "autoAudits": false, "useApiHook": true, "showShadowRootInWxmlPanel": true, "useStaticServer": false, "useLanDebug": false, "showES6CompileOption": false, "bigPackageSizeSupport": false, "checkInvalidKey": true, "ignoreDevUnusedFiles": true}, "libVersion": "3.9.2", "condition": {"miniprogram": {"current": 0, "list": [{"name": "pages/index/index", "pathName": "pages/index/index", "query": "", "scene": null, "launchMode": "default"}, {"name": "首页", "pathName": "pages/index/index", "query": ""}]}}}