const BASE_URL = '';

const StorageKeys = {
  BoundDevices: 'BOUND_DEVICES',
};

function request({ url, method = 'GET', data = {}, header = {} }) {
  return new Promise((resolve, reject) => {
    wx.request({
      url: `${BASE_URL}${url}`,
      method,
      data,
      header: {
        'Content-Type': 'application/json',
        ...header,
      },
      success: (res) => {
        if (res.statusCode >= 200 && res.statusCode < 300) {
          resolve(res.data);
        } else {
          reject(res);
        }
      },
      fail: reject,
    });
  });
}

function getBoundDevices() {
  try {
    const list = wx.getStorageSync(StorageKeys.BoundDevices);
    return Array.isArray(list) ? list : [];
  } catch (e) {
    return [];
  }
}

function saveBoundDevices(list) {
  wx.setStorageSync(StorageKeys.BoundDevices, list || []);
}

function upsertDevice(device) {
  const list = getBoundDevices();
  const index = list.findIndex((d) => d.deviceId === device.deviceId);
  if (index >= 0) {
    list[index] = { ...list[index], ...device };
  } else {
    list.unshift(device);
  }
  saveBoundDevices(list);
  return list;
}

module.exports = {
  request,
  StorageKeys,
  getBoundDevices,
  saveBoundDevices,
  upsertDevice,
};