const { upsertDevice } = require('../../utils/api');

Page({
  data: {},
  onScan() {
    wx.scanCode({
      onlyFromCamera: true,
      success: (res) => {
        try {
          const payload = parseScanResult(res.result);
          const device = {
            deviceId: payload.deviceId,
            name: payload.name || '智能灯',
            model: payload.model || 'WallSwitch-Mini',
            online: true,
            lastSyncAt: Date.now(),
          };
          upsertDevice(device);
          wx.showToast({ title: '绑定成功', icon: 'success' });
          setTimeout(() => {
            wx.redirectTo({ url: `/pages/device/index?deviceId=${device.deviceId}` });
          }, 500);
        } catch (e) {
          wx.showToast({ title: '二维码无效', icon: 'error' });
        }
      },
      fail: () => wx.showToast({ title: '已取消', icon: 'none' }),
    });
  },
});

function parseScanResult(result) {
  try {
    if (!result) throw new Error('empty');
    if (/^\{/.test(result)) {
      const obj = JSON.parse(result);
      if (!obj.deviceId) throw new Error('no deviceId');
      return obj;
    }
    const url = new URL(result);
    const params = {};
    (url.search || '').replace(/^\?/, '').split('&').forEach((pair) => {
      if (!pair) return;
      const [k, v] = pair.split('=');
      params[decodeURIComponent(k)] = decodeURIComponent(v || '');
    });
    if (!params.deviceId) throw new Error('no deviceId');
    return {
      deviceId: params.deviceId,
      name: params.name,
      model: params.model,
    };
  } catch (e) {
    throw e;
  }
}