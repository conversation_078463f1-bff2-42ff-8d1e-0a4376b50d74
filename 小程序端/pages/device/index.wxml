<view class="page">
  <view class="header">
    <text class="name">{{device.name}}</text>
    <text class="sub">{{device.model}} · {{device.online ? '在线' : '离线'}}</text>
  </view>

  <view class="device-card">
    <view class="power">
      <switch checked="{{isOn}}" bindchange="onToggle" color="#111" />
      <text class="state">{{isOn ? '已开启' : '已关闭'}}</text>
    </view>
  </view>

  <view class="actions">
    <button size="mini" bindtap="goSettings">更多设置</button>
  </view>
</view>