const { getBoundDevices, upsertDevice } = require('../../utils/api');

Page({
  data: {
    device: {},
    isOn: false,
  },
  onLoad(query) {
    const { deviceId } = query || {};
    const list = getBoundDevices();
    const device = list.find((d) => d.deviceId === deviceId) || list[0];
    if (!device) {
      wx.showToast({ title: '未找到设备', icon: 'none' });
      setTimeout(() => wx.redirectTo({ url: '/pages/index/index' }), 800);
      return;
    }
    this.setData({ device, isOn: !!device.isOn });
  },
  onToggle(e) {
    const isOn = !!e.detail.value;
    this.setData({ isOn });
    const { device } = this.data;
    upsertDevice({ ...device, isOn, lastSyncAt: Date.now() });
    wx.showToast({ title: isOn ? '已开启' : '已关闭', icon: 'none' });
  },
  goSettings() {
    const { device } = this.data;
    wx.navigateTo({ url: `/pages/settings/index?deviceId=${device.deviceId}` });
  },
});