page { background: #f7f7f7; }
.page { padding: 24rpx 32rpx 48rpx; }
.header { padding: 24rpx 0; }
.name { font-size: 36rpx; color: #111; font-weight: 600; }
.sub { display: block; margin-top: 8rpx; color: #888; font-size: 26rpx; }
.device-card { margin-top: 24rpx; background: #fff; border-radius: 24rpx; padding: 40rpx 0; display: flex; align-items: center; justify-content: center; }
.power { display: flex; flex-direction: column; align-items: center; }
.state { margin-top: 16rpx; color: #111; font-size: 28rpx; }
.actions { margin-top: 24rpx; text-align: center; }