/**index.wxss**/
page { background: #f2f4f6; }
.page { padding: 16rpx 24rpx 120rpx; }
.topbar { display: flex; align-items: center; justify-content: space-between; padding: 16rpx 0; }
.home-selector { display: flex; align-items: center; gap: 8rpx; }
.home-name { font-size: 36rpx; color: #111; font-weight: 700; }
.arrow { color: #666; margin-left: 8rpx; }
.top-actions { display: flex; align-items: center; gap: 12rpx; }
.icon-btn { background: #fff; border-radius: 999rpx; padding: 0 20rpx; }

.rooms { margin-top: 8rpx; white-space: nowrap; position: relative; }
.room-tabs { display: inline-flex; gap: 24rpx; padding-bottom: 8rpx; }
.room-tab { color: #96a0aa; padding: 8rpx 8rpx; font-size: 28rpx; }
.room-tab.active { color: #111; font-weight: 600; }
.room-tab.add { color: #2b77ff; }
.tab-indicator { position: absolute; height: 6rpx; background: #2b77ff; border-radius: 6rpx; bottom: 0; left: 0; transition: transform 200ms ease, width 200ms ease; }

.location { margin: 16rpx 0 8rpx; }
.location-text { display: inline-block; color: #5f7aa1; background: #e8f0ff; padding: 10rpx 18rpx; border-radius: 999rpx; font-size: 24rpx; }

.grid { display: grid; grid-template-columns: repeat(2, 1fr); gap: 24rpx; margin-top: 16rpx; opacity: 1; transition: opacity 200ms ease; }
.grid.fade { opacity: 0; }
.card { background: #fff; border-radius: 28rpx; padding: 28rpx; min-height: 220rpx; display: flex; flex-direction: column; justify-content: space-between; }
.icon { width: 96rpx; height: 96rpx; opacity: .9; }
.icon-placeholder { width: 96rpx; height: 96rpx; display: flex; align-items: center; justify-content: center; font-size: 56rpx; }
.meta { margin-top: 24rpx; }
.name { font-size: 32rpx; color: #111; font-weight: 600; }
.sub { margin-top: 6rpx; color: #8a8a8a; font-size: 24rpx; }

.empty { display: flex; flex-direction: column; align-items: center; padding: 160rpx 0; color: #888; }
.tip { margin-bottom: 24rpx; }
.scan-main { background: #111; color: #fff; border-radius: 999rpx; }

.edit-wrap { display: flex; justify-content: center; margin: 28rpx 0 0; }
.edit-btn { background: #fff; border-radius: 999rpx; padding: 8rpx 28rpx; color: #333; }
