// index.js
const { getBoundDevices } = require('../../utils/api');

Page({
  data: {
    homeName: 'Giao的家',
    rooms: [
      { name: '全屋' },
      { name: '客厅' },
      { name: '卧室' },
      { name: '未分配房间' },
    ],
    currentRoom: '全屋',
    grid: [],
    indicatorLeft: 0,
    indicatorWidth: 0,
  },
  onShow() {
    this.loadDevices();
    this.$nextTick && this.$nextTick(() => this.updateIndicator());
  },
  onPullDownRefresh() {
    this.loadDevices();
    wx.stopPullDownRefresh();
  },
  loadDevices() {
    const devices = getBoundDevices();
    const currentRoom = this.data.currentRoom;
    const list = devices
      .filter((d) => currentRoom === '全屋' || d.room === currentRoom)
      .map((d) => ({
        deviceId: d.deviceId,
        name: d.name || '智能灯',
        room: d.room || '未分配房间',
        icon: d.icon,
        placeholder: pickPlaceholder(d.type),
      }));
    this.setData({ grid: list, gridFadeClass: '' });
  },
  onSwitchRoom(e) {
    const name = e.currentTarget.dataset.name;
    this.setData({ currentRoom: name, gridFadeClass: 'fade' }, () => {
      setTimeout(() => this.loadDevices(), 120);
      this.updateIndicator(name);
    });
  },
  updateIndicator(targetName) {
    const query = wx.createSelectorQuery();
    query.selectAll('.room-tab').boundingClientRect();
    query.select('.rooms').scrollOffset();
    query.exec((res) => {
      const tabs = res[0] || [];
      const scroll = res[1] ? res[1].scrollLeft : 0;
      const name = targetName || this.data.currentRoom;
      const tab = tabs.find((t) => t.dataset && t.dataset.name === name);
      if (!tab) return;
      const left = tab.left - tabs[0].left - scroll;
      this.setData({ indicatorLeft: left, indicatorWidth: tab.width });
    });
  },
  goScan() {
    wx.navigateTo({ url: '/pages/bind/index' });
  },
  openDevice(e) {
    const id = e.currentTarget.dataset.id;
    if (!id) return;
    wx.navigateTo({ url: `/pages/device/index?deviceId=${id}` });
  },
  onEdit() {
    wx.showToast({ title: '编辑模式敬请期待', icon: 'none' });
  },
  onMessage() {
    wx.showToast({ title: '消息中心暂未实现', icon: 'none' });
  },
  onSelectHome() {
    wx.showToast({ title: '切换家庭暂未实现', icon: 'none' });
  },
  onAddRoom() {
    const that = this;
    wx.showModal({
      title: '新增房间',
      editable: true,
      placeholderText: '请输入房间名',
      success(res) {
        if (res.confirm && res.content) {
          const rooms = that.data.rooms.concat({ name: res.content });
          that.setData({ rooms }, () => that.updateIndicator());
        }
      },
    });
  },
});

function pickPlaceholder(type) {
  switch (type) {
    case 'light':
      return '💡';
    case 'router':
      return '📶';
    case 'plug':
      return '🔌';
    default:
      return '📟';
  }
}
