<view class="page">
  <view class="topbar">
    <view class="home-selector" bindtap="onSelectHome">
      <text class="home-name">{{homeName}}</text>
      <text class="arrow">▾</text>
    </view>
    <view class="top-actions">
      <button class="icon-btn" size="mini" bindtap="onMessage">💬</button>
      <button class="icon-btn" size="mini" bindtap="goScan">＋</button>
    </view>
  </view>

  <scroll-view class="rooms" scroll-x>
    <view class="room-tabs">
      <block wx:for="{{rooms}}" wx:key="name">
        <view class="room-tab {{currentRoom===item.name?'active':''}}" data-name="{{item.name}}" bindtap="onSwitchRoom">
          <text>{{item.name}}</text>
        </view>
      </block>
      <view class="room-tab add" bindtap="onAddRoom"><text>＋</text></view>
    </view>
    <view class="tab-indicator" style="transform: translateX({{indicatorLeft}}px); width: {{indicatorWidth}}px;"></view>
  </scroll-view>


  <block wx:if="{{grid.length}}">
    <view class="grid {{gridFadeClass}}">
      <block wx:for="{{grid}}" wx:key="deviceId">
        <view class="card" bindtap="openDevice" data-id="{{item.deviceId}}">
          <block wx:if="{{item.icon}}">
            <image class="icon" src="{{item.icon}}" mode="aspectFit"/>
          </block>
          <block wx:else>
            <view class="icon-placeholder">{{item.placeholder}}</view>
          </block>
          <view class="meta">
            <text class="name">{{item.name}}</text>
            <text class="sub">{{item.room}}</text>
          </view>
        </view>
      </block>
    </view>
  </block>

  <block wx:else>
    <view class="empty">
      <text class="tip">暂无设备</text>
      <button class="scan-main" bindtap="goScan">添加设备</button>
    </view>
  </block>


</view>
