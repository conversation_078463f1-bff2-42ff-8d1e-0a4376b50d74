const { getBoundDevices, saveBoundDevices, upsertDevice } = require('../../utils/api');

Page({
  data: {
    deviceId: '',
    name: '',
  },
  onLoad(query) {
    const { deviceId } = query || {};
    const list = getBoundDevices();
    const device = list.find((d) => d.deviceId === deviceId) || list[0];
    if (!device) {
      wx.showToast({ title: '未找到设备', icon: 'none' });
      setTimeout(() => wx.redirectTo({ url: '/pages/index/index' }), 800);
      return;
    }
    this.setData({ deviceId: device.deviceId, name: device.name || '' });
  },
  onNameInput(e) {
    this.setData({ name: e.detail.value });
  },
  onUnload() {
    const { deviceId, name } = this.data;
    if (!deviceId) return;
    upsertDevice({ deviceId, name });
  },
  onUnbind() {
    const { deviceId } = this.data;
    const list = getBoundDevices().filter((d) => d.deviceId !== deviceId);
    saveBoundDevices(list);
    wx.showToast({ title: '已解绑', icon: 'none' });
    setTimeout(() => wx.redirectTo({ url: '/pages/index/index' }), 600);
  },
});