page { background: #f7f7f7; }
.page { padding: 24rpx 32rpx 48rpx; }
.card { background: #fff; border-radius: 24rpx; padding: 16rpx 24rpx; }
.row { display: flex; align-items: center; padding: 24rpx 0; border-bottom: 1px solid #f2f2f2; }
.row:last-child { border-bottom: 0; }
.label { width: 180rpx; color: #666; }
.ipt { flex: 1; color: #111; }
.val { flex: 1; color: #111; text-align: right; }
.danger { margin-top: 32rpx; }
.btn-danger { background: #fff2f2; color: #d23; border-radius: 16rpx; }